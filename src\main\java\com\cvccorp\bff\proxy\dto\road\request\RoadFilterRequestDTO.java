package com.cvccorp.bff.proxy.dto.road.request;

import com.cvccorp.bff.infra.enums.SortOrderEnum;
import com.cvccorp.bff.infra.enums.TimeRangeEnum;
import com.cvccorp.bff.infra.validation.MaxPeriod;
import com.cvccorp.bff.proxy.dto.promotion.SelectedItemDTO;
import com.cvccorp.bff.proxy.dto.promotion.request.OpportunityParamDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.FutureOrPresent;
import javax.validation.constraints.NotNull;
import javax.ws.rs.QueryParam;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
public class RoadFilterRequestDTO {

    @NotNull
    @QueryParam("ages")
    private List<Integer> ages;

    @NotNull
    @QueryParam("arrivalMasterCode")
    private String arrivalMasterCode;

    @NotNull
    @QueryParam("departureMasterCode")
    private String departureMasterCode;

    @FutureOrPresent
    @MaxPeriod
    @QueryParam("departureDate")
    private LocalDate departureDate;

    @QueryParam("arrivalFilter")
    private Set<Long> arrivalFilter = Collections.emptySet();

    @QueryParam("companyFilter")
    private Set<Long> companyFilter = Collections.emptySet();

    @QueryParam("departureFilter")
    private Set<Long> departureFilter = Collections.emptySet();

    @QueryParam("serviceClassFilter")
    private Set<String> serviceClassFilter = Collections.emptySet();

    @QueryParam("timeRangeFilter")
    private Set<TimeRangeEnum> timeRangeFilter = Collections.emptySet();

    private List<SelectedItemDTO> promoSelectedItems;

    private OpportunityParamDTO promoOpportunityParam;

    @QueryParam("brokerId")
    private Long brokerId;

    @QueryParam("clearCache")
    private Boolean clearCache;

    @QueryParam("minPriceFilter")
    private BigDecimal minPrice;

    @QueryParam("maxPriceFilter")
    private BigDecimal maxPrice;

    @QueryParam("sortOrder")
    private SortOrderEnum sortOrder;

    @QueryParam("curPage")
    private Integer curPage;

    @QueryParam("pageSize")
    private Integer pageSize;
}
