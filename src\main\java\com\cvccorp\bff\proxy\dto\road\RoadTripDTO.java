package com.cvccorp.bff.proxy.dto.road;

import com.cvccorp.bff.proxy.dto.promotion.PromotionDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class RoadTripDTO {
    private String tripId;
    private String tripToken;
    private RoadTripStepDTO departure;
    private RoadTripStepDTO arrival;
    private RoadSupplierDTO supplier;
    private String serviceClass;
    private String duration;
    private Long brokerId;
    private String rateToken;
    private boolean preferential;
    private BigDecimal priceWithoutTax;
    private BigDecimal priceWithTax;
    private BigDecimal amountTax;
    private List<Object> errors;
    private Boolean hasCombo;
    private PromotionDTO promotion;
}
