package com.cvccorp.bff.service.road;

import com.cvccorp.bff.infra.exception.ClientExceptionMapper;
import com.cvccorp.bff.infra.filters.RequestHeadersFactory;
import com.cvccorp.bff.proxy.dto.road.RoadAvailTablePriceResponseDTO;
import com.cvccorp.bff.proxy.dto.road.SeatBlockDTO;
import com.cvccorp.bff.proxy.dto.road.request.RoadFilterRequestDTO;
import com.cvccorp.bff.proxy.dto.road.request.RoadTablePriceFilterRequestDTO;
import com.cvccorp.bff.proxy.dto.road.response.RoadListResponseDTO;
import com.cvccorp.bff.proxy.dto.road.response.SeatBlockResponseDTO;
import com.cvccorp.bff.proxy.dto.road.response.SeatResponseDTO;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.rest.client.annotation.RegisterClientHeaders;
import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;

@RegisterRestClient(configKey = "rest-client-getway-road")
@RegisterClientHeaders(RequestHeadersFactory.class)
@RegisterProvider(ClientExceptionMapper.class)
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public interface RoadClient {

    @GET
    @Path("/roads")
    RoadListResponseDTO roads(@BeanParam RoadFilterRequestDTO roadFilterTO);

    @GET
    @Path("/roads/table-price")
    List<RoadAvailTablePriceResponseDTO> roadsTablePrice(@BeanParam RoadTablePriceFilterRequestDTO roadFilterTO);

    @GET
    @Path("/road/seats/{tripToken}")
    Uni<SeatResponseDTO> seats(@PathParam("tripToken") String tripToken);

    @POST
    @Path("/road/seat")
    Uni<SeatBlockResponseDTO> reservationSeats(SeatBlockDTO seatBlock);

}
