package com.cvccorp.bff.proxy.dto.road.request;

import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.ws.rs.QueryParam;
import java.util.List;

@Setter
@NoArgsConstructor
public class RoadTablePriceFilterRequestDTO {

    @NotNull
    @QueryParam("arrivalMasterCode")
    private String arrivalMasterCode;

    @NotNull
    @QueryParam("departureMasterCode")
    private String departureMasterCode;

    @NotBlank
    @QueryParam("departureDate")
    private String departureDate;

    @NotNull
    @QueryParam("ages")
    private List<Integer> ages;

}
