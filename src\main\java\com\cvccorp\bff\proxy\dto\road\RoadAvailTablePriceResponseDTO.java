package com.cvccorp.bff.proxy.dto.road;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Set;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public class RoadAvailTablePriceResponseDTO {
    private LocalDate date;
    private Set<TablePriceSupplier> tablePriceSuppliers;

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TablePriceSupplier {
        private String id;
        private String name;
        private BigDecimal priceWithTax;
    }
}
