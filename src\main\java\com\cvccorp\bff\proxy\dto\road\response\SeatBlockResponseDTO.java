package com.cvccorp.bff.proxy.dto.road.response;

import com.cvccorp.bff.proxy.dto.road.SeatBlockDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

@Getter
@NoArgsConstructor
public class SeatBlockResponseDTO {

    private final List<SeatBlockDTO.Pax> paxs = Collections.emptyList();
    private final List<SeatBlockDTO.RoadReserveTrip> trips = Collections.emptyList();

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<SeatBlockResponseError> errors;

    @Getter
    @NoArgsConstructor
    public static class SeatBlockResponseError {

        private String code;
        private String message;
    }
}
