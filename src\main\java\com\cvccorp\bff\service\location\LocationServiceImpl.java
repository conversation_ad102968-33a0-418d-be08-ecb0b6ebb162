package com.cvccorp.bff.service.location;

import com.cvccorp.bff.infra.util.FunctionUtil;
import com.cvccorp.bff.proxy.dto.location.LocationResponseDTO;
import com.cvccorp.bff.proxy.interfaces.LocationService;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.cvccorp.bff.infra.util.FunctionUtil.capitalizeDescription;
import static com.cvccorp.bff.infra.util.Operations.FIND_LOCATIONS;

@RequestScoped
public class LocationServiceImpl implements LocationService {
    private final String nameClass = this.getClass().getSuperclass().getSimpleName();

    @Inject
    Logger logger;

    @RestClient
    LocationClient locationClient;

    public List<LocationResponseDTO> findLocations(Integer quant, String name) {
        quant = quant == null ? 25 : quant;
        FunctionUtil.printLoggerInfo(logger, nameClass.concat(".findLocations"),
                "quantFilter:" + quant + ", nameFilter:" + name,
                new Object() {} .getClass().getEnclosingMethod().getName(), FIND_LOCATIONS);
        try {
            List<LocationResponseDTO> locationResponseDTOList = locationClient.findLocations()
                    .stream()
                    .filter(item -> {
                        if (item.getTypeId() == 1 || item.getTypeId() == 4 || item.getTypeId() == 5) {
                            if (!FunctionUtil.empty(name)) {
                                return FunctionUtil.compareStringIgnoreCase(item.getName(), name);
                            }
                            return true;
                        }
                        return false;
                    })
                    .limit(quant)
                    .collect(Collectors.toList());
            locationResponseDTOList.forEach(locationResponseDTO -> {
                String currentName = locationResponseDTO.getName();
                String newName = capitalizeDescription(currentName);
                locationResponseDTO.setName(newName);
            });
            return locationResponseDTOList;
        } catch (Exception ex) {
            FunctionUtil.printLoggerError(logger, nameClass.concat(".findLocations"),
                    ex.getMessage(),
                    new Object() {} .getClass().getEnclosingMethod().getName(),
                    FIND_LOCATIONS);
            return Collections.emptyList();
        }
    }
}
