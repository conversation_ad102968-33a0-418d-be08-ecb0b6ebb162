package com.cvccorp.bff.infra.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target(FIELD)
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = MaxPeriodValidator.class)
public @interface MaxPeriod {

    int maxDays() default 360;

    String message() default "Periodo maximo para consulta é de {maxDays} dias.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
