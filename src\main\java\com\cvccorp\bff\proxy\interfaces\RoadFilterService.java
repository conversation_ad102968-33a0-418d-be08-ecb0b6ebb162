package com.cvccorp.bff.proxy.interfaces;

import com.cvccorp.bff.proxy.dto.road.grouped.RoadGroupedResponseDTO;
import com.cvccorp.bff.proxy.dto.road.request.RoadFilterRequestDTO;
import com.cvccorp.bff.proxy.dto.road.response.RoadListResponseDTO;

public interface RoadFilterService {
    RoadListResponseDTO findRoadsByFilter(RoadFilterRequestDTO roadFilterTO);
    RoadGroupedResponseDTO findRoadsByFilterGrouped(RoadFilterRequestDTO roadFilterTO);
}
