package com.cvccorp.bff.service.road;

import com.cvccorp.bff.infra.util.FunctionUtil;
import com.cvccorp.bff.proxy.dto.road.SeatBlockDTO;
import com.cvccorp.bff.proxy.dto.road.response.SeatBlockResponseDTO;
import com.cvccorp.bff.proxy.dto.road.response.SeatResponseDTO;
import com.cvccorp.bff.proxy.interfaces.SeatService;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

import java.util.Collections;

import static com.cvccorp.bff.infra.util.Operations.FIND_SEATS;
import static com.cvccorp.bff.infra.util.Operations.RESERVATION_SEATS;

@RequestScoped
public class SeatServiceImpl implements SeatService {
    private final String nameClass = this.getClass().getSuperclass().getSimpleName();

    @Inject
    Logger logger;

    @RestClient
    RoadClient gtwRoadClient;

    @Override
    public Uni<SeatResponseDTO> seats(String tripToken) {
        FunctionUtil.printLoggerInfo(logger, nameClass.concat(".seats"), "Iniciando busca de assentos.",
                new Object() {} .getClass().getEnclosingMethod().getName(), FIND_SEATS);
        try {
            return gtwRoadClient.seats(tripToken)
                    .onFailure().transform(Throwable::getCause);
        } catch (Exception ex) {
            FunctionUtil.printLoggerError(logger, nameClass.concat(".seats"),
                    ex.getMessage(),
                    new Object() {} .getClass().getEnclosingMethod().getName(),
                    FIND_SEATS);
            return Uni.createFrom().item(new SeatResponseDTO());
        }
    }

    @Override
    public Uni<SeatBlockResponseDTO> reservationSeats(SeatBlockDTO seatBlockRequestTO) {
        FunctionUtil.printLoggerInfo(logger, nameClass.concat(".reservationSeats"), "Iniciando reserva de assentos.",
                new Object() {} .getClass().getEnclosingMethod().getName(), RESERVATION_SEATS);
        try {
            return gtwRoadClient.reservationSeats(seatBlockRequestTO)
                    .onFailure().transform(Throwable::getCause);
        } catch (Exception ex) {
            FunctionUtil.printLoggerError(logger, nameClass.concat(".reservationSeats"),
                    ex.getMessage(),
                    new Object() {} .getClass().getEnclosingMethod().getName(),
                    RESERVATION_SEATS);
            return Uni.createFrom().item(new SeatBlockResponseDTO());
        }
    }

}
