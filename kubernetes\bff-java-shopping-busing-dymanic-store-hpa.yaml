apiVersion: autoscaling/v2beta2
kind: HorizontalPodAutoscaler
metadata:
  name: bff-java-shopping-busing-dymanic-store-hpa
  namespace: corp-shopping-busing
  labels:
    app: bff-java-shopping-busing-dymanic-store
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: bff-java-shopping-busing-dymanic-store-deploy
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 500
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 250