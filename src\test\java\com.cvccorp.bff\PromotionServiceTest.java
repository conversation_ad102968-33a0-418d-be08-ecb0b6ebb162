package com.cvccorp.bff;

import com.cvccorp.bff.proxy.dto.promotion.request.OpportunityRequestDTO;
import com.cvccorp.bff.proxy.dto.promotion.response.OpportunitiesResponseDTO;
import com.cvccorp.bff.proxy.interfaces.PromotionService;
import com.cvccorp.bff.service.promotion.PromotionClient;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.TestProfile;
import io.quarkus.test.junit.mockito.InjectMock;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.junit.jupiter.api.Test;

import javax.inject.Inject;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@QuarkusTest
@TestProfile(CustomTestProfile.class)
public class PromotionServiceTest {

    @Inject
    PromotionService promotionService;

    @InjectMock
    @RestClient
    PromotionClient promotionClient;

    @Test
    void getOpportunities() {
        OpportunitiesResponseDTO opportunitiesResponseDTO = new OpportunitiesResponseDTO();
        when(promotionClient.opportunities(anyString())).thenReturn(opportunitiesResponseDTO);
        assertNotNull(promotionService.getOpportunities(new OpportunityRequestDTO()));

    }
}
