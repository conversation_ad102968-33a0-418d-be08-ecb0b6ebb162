package com.cvccorp.bff.proxy.dto.road.grouped;

import com.cvccorp.bff.proxy.dto.road.CancelPolicyDTO;
import com.cvccorp.bff.proxy.dto.road.SearchMetaDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class RoadGroupedResponseDTO {
    private List<RoadGroupedTripDTO> trips = Collections.emptyList();
    private SearchMetaDTO meta;
    private List<CancelPolicyDTO> cancelPolicies = Collections.emptyList();
}
