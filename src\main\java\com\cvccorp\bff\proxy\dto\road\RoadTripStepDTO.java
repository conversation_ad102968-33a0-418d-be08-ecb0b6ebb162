package com.cvccorp.bff.proxy.dto.road;

import com.cvccorp.bff.infra.enums.TimeRangeEnum;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.LocalTime;

@Getter
@Setter
@NoArgsConstructor
public class RoadTripStepDTO {
    private RoadLocationDTO location;
    private LocalDateTime dateTime;
    private String timezone;
    private LocalTime time;
    private String date;
    private TimeRangeEnum timeRange;
}
