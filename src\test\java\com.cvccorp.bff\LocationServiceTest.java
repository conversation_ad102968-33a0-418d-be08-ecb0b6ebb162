package com.cvccorp.bff;

import com.cvccorp.bff.proxy.dto.location.LocationResponseDTO;
import com.cvccorp.bff.proxy.interfaces.LocationService;
import com.cvccorp.bff.service.location.LocationClient;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.TestProfile;
import io.quarkus.test.junit.mockito.InjectMock;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.junit.jupiter.api.Test;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.Mockito.when;

@QuarkusTest
@TestProfile(CustomTestProfile.class)
public class LocationServiceTest {

    @Inject
    LocationService locationService;

    @InjectMock
    @RestClient
    LocationClient locationClient;

    @Test
    void findLocations() {
        List<LocationResponseDTO> list = new ArrayList<>();
        LocationResponseDTO locationTO = new LocationResponseDTO();
        locationTO.setTypeId(1L);
        locationTO.setName("name");
        list.add(locationTO);

        when(locationClient.findLocations()).thenReturn(list);

        assertFalse(locationService.findLocations(1, "name").isEmpty());

    }
}
