package com.cvccorp.bff.proxy.dto.road.grouped;

import com.cvccorp.bff.proxy.dto.road.RoadTripDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class RoadGroupedTripDTO {
    private String tripId;
    private String serviceClass;
    private BigDecimal priceWithoutTax;
    private RoadGroupedSupplierDTO supplier;
    private RoadGroupedLocationDTO arrival;
    private RoadGroupedLocationDTO departure;
    private List<RoadTripDTO> timetable = new ArrayList<>();
}
