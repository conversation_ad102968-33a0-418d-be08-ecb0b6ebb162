package com.cvccorp.bff.proxy.dto.road;

import com.cvccorp.bff.infra.enums.TimeRangeEnum;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Set;

@Getter
@NoArgsConstructor
public class FilterInfoDTO {
    private Set<TimeRangeEnum> timeRange;
    private Set<Long> companies;
    private Set<String> serviceClass;
    private Set<Long> departureLocation;
    private Set<Long> arrivalLocation;
    private PriceRangeDTO priceRange;
}
