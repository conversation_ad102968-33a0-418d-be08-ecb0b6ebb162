package com.cvccorp.bff.service.road;

import com.cvccorp.bff.infra.util.FunctionUtil;
import com.cvccorp.bff.proxy.dto.road.RoadAvailTablePriceResponseDTO;
import com.cvccorp.bff.proxy.dto.road.request.RoadTablePriceFilterRequestDTO;
import com.cvccorp.bff.proxy.interfaces.RoadTablePriceService;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.cvccorp.bff.infra.util.Operations.FIND_TABLE_PRICES;


@RequestScoped
public class RoadTablePriceServiceImpl implements RoadTablePriceService {
    private final String nameClass = this.getClass().getSuperclass().getSimpleName();

    @Inject
    Logger logger;

    @RestClient
    RoadClient gtwRoadClient;

    @Override
    public List<RoadAvailTablePriceResponseDTO> findRoadsTablePrice(RoadTablePriceFilterRequestDTO filter) {
        FunctionUtil.printLoggerInfo(logger, nameClass.concat(".findRoadsTablePrice"), "Iniciando busca de Table Prices.",
                new Object() {} .getClass().getEnclosingMethod().getName(), FIND_TABLE_PRICES);
        try {
            List<RoadAvailTablePriceResponseDTO> response = gtwRoadClient.roadsTablePrice(filter);

            // Filtra todas as aviações
            Map<String, String> allCompanys = new HashMap<>();
            response.forEach(item -> {
                item.getTablePriceSuppliers().forEach(subItem -> {
                    if (!allCompanys.containsKey(subItem.getName())) {
                        allCompanys.put(subItem.getName(), subItem.getName());
                    }
                });
            });

            // Equaliza table price
            allCompanys.keySet().forEach(key -> {
                response.forEach(availTablePrice -> {

                    RoadAvailTablePriceResponseDTO.TablePriceSupplier tablePriceFound = availTablePrice.getTablePriceSuppliers().stream()
                            .filter(tablePriceSupplier -> tablePriceSupplier.getName().equals(key))
                            .findFirst()
                            .orElse(null);

                    if (tablePriceFound == null) {
                        availTablePrice.getTablePriceSuppliers().add(
                                new RoadAvailTablePriceResponseDTO.TablePriceSupplier(
                                        key,
                                        allCompanys.get(key),
                                        null
                                )
                        );
                    }
                });
            });

            return response;
        } catch (Exception ex) {
            FunctionUtil.printLoggerError(logger, nameClass.concat(".findRoadsTablePrice"),
                    ex.getMessage(),
                    new Object() {} .getClass().getEnclosingMethod().getName(),
                    FIND_TABLE_PRICES);
            return Collections.emptyList();
        }
    }

}
