package com.cvccorp.bff.infra.exception;

import com.cvccorp.bff.infra.util.FunctionUtil;

import javax.ws.rs.core.Response;

public class BusinessException extends RuntimeException {
    private Integer status;
    private String transactionId;
    private String path;

    public BusinessException(Response.Status status, String message) {
        super(message);
        this.status = status.getStatusCode();
        this.transactionId = FunctionUtil.getTransactionIdUserContext();
    }

    public BusinessException(Integer status, String message) {
        super(message);
        this.status = status;
        this.transactionId = FunctionUtil.getTransactionIdUserContext();
    }

    public BusinessException(Response.Status status, String message, String transactionId) {
        super(message);
        this.status = status.getStatusCode();
        this.transactionId = transactionId;
    }

    public BusinessException(Response.Status status, String message, String transactionId, String path) {
        super(message);
        this.status = status.getStatusCode();
        this.transactionId = transactionId;
        this.path = path;
    }

    public BusinessException(Throwable cause) {
        super(cause);
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Response.Status status) {
        this.status = status.getStatusCode();
    }

    public String getTransactionId() {
        return transactionId;
    }

    public String getPath() {
        return path;
    }

}
