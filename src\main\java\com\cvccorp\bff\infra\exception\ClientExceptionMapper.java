package com.cvccorp.bff.infra.exception;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.eclipse.microprofile.rest.client.ext.ResponseExceptionMapper;
import org.jboss.logging.Logger;

import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.util.Map;

public class ClientExceptionMapper implements ResponseExceptionMapper<RuntimeException> {

    @Inject
    Logger logger;

    @Override
    public RuntimeException toThrowable(Response response) {
        logger.error(String.format("m=ClientExceptionMapper.toThrowable,  message: %s", response.readEntity(String.class)));
        throw new BusinessException(response.getStatus(), getMessageExceptionClient(response.readEntity(String.class)));
    }

    private String getMessageExceptionClient(String exceptionJson){
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> erroClientMap = objectMapper.readValue(exceptionJson, new TypeReference<>() {});
            if(erroClientMap.containsKey("stack")){
                return String.format("gtw-error: %s", erroClientMap.get("details"));
            }
            if(erroClientMap.containsKey("message")){
                return String.format("gtw-error: %s", erroClientMap.get("message"));
            }
            return String.format("gtw-error: %s", "Not message erro client");
        } catch (JsonProcessingException ex) {
            logger.error(String.format("m=parseStringException,  message: %s", ex.getMessage()));
            return String.format("gtw-error: %s", "Not message erro client");
            //throw new BusinessException(Response.Status.BAD_REQUEST, "Falha ao realizar parse string exception");
        }
    }


}
