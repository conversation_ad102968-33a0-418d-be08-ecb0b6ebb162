package com.cvccorp.bff.proxy.dto.road.response;

import com.cvccorp.bff.proxy.dto.road.CancelPolicyDTO;
import com.cvccorp.bff.proxy.dto.road.RoadTripDTO;
import com.cvccorp.bff.proxy.dto.road.SearchMetaDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class RoadListResponseDTO {
    private List<RoadTripDTO> trips = Collections.emptyList();
    private SearchMetaDTO meta;
    private List<CancelPolicyDTO> cancelPolicies = Collections.emptyList();
}
