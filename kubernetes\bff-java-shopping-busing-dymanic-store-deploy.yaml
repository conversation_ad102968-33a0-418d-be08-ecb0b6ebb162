apiVersion: apps/v1
kind: Deployment
metadata:
  name: bff-java-shopping-busing-dymanic-store-deploy
  namespace: corp-shopping-busing
  labels:
    app: bff-java-shopping-busing-dymanic-store
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bff-java-shopping-busing-dymanic-store
  template:
    metadata:
      labels:
        app: bff-java-shopping-busing-dymanic-store
      annotations:
        vault.security.banzaicloud.io/vault-addr: __VAULT_ADDR__
    spec:
      serviceAccountName: bff-java-shopping-busing-dymanic-store
      containers:
      - name: bff-java-shopping-busing-dymanic-store
        image: ************.dkr.ecr.sa-east-1.amazonaws.com/bff-java-shopping-busing-dymanic-store:__TAG__
        imagePullPolicy: Always
        resources:
          requests:
            memory: "256Mi"
            cpu: "80m"
          limits:
            memory: "800Mi"
            cpu: "500m"
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /q/health
            port: 8080
            httpHeaders:
            - name: X-Custom-Header
              value: ReadinessProbe
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 10
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /q/health
            port: 8080
            httpHeaders:
            - name: X-Custom-Header
              value: LivenessProbe
          initialDelaySeconds: 35
          periodSeconds: 15
          successThreshold: 1
          timeoutSeconds: 10
        envFrom:
          - configMapRef:
              name: bff-java-shopping-busing-dymanic-store
          - secretRef:
              name: bff-java-shopping-busing-dymanic-store
        ports:
        - containerPort: 8080
        - containerPort: 5005
