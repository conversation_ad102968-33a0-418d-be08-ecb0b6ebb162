package com.cvccorp.bff.proxy.dto.road;

import com.cvccorp.bff.infra.enums.GenderEnum;
import com.cvccorp.bff.infra.enums.PaxDocumentEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@NoArgsConstructor
public class SeatBlockDTO {

    private List<Pax> paxs;
    private List<RoadReserveTrip> trips;

    @Getter
    @NoArgsConstructor
    public static class Pax {

        private Integer id;
        private String firstName;
        private String lastName;
        private GenderEnum gender;
        private PaxDocument document;

        @Getter
        @NoArgsConstructor
        public static class PaxDocument {

            private String doc;
            private PaxDocumentEnum type;
            private String issuingState;
        }
    }

    @Getter
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RoadReserveTrip {

        private Integer rph;
        private String rateToken;
        private String tripToken;
        private List<RoadReserveSeat> reservations;

        @Getter
        @NoArgsConstructor
        public static class RoadReserveSeat {

            private Integer paxId;
            private Integer seatNumber;
            @JsonInclude(JsonInclude.Include.NON_NULL)
            private Boolean deleteFlag;
            @JsonInclude(JsonInclude.Include.NON_NULL)
            private String reservationToken;
            @JsonInclude(JsonInclude.Include.NON_NULL)
            private String errorMessage;
        }
    }
}
