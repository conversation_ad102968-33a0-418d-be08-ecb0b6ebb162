package com.cvccorp.bff.service.location;

import com.cvccorp.bff.infra.util.FunctionUtil;
import com.cvccorp.bff.proxy.dto.location.LocationResponseDTOV2;
import com.cvccorp.bff.proxy.interfaces.LocationServiceV2;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.util.Collections;
import java.util.List;

import static com.cvccorp.bff.infra.util.Operations.FIND_LOCATIONS;

@RequestScoped
public class LocationServiceV2Impl implements LocationServiceV2 {
    private final String nameClass = this.getClass().getSuperclass().getSimpleName();

    @Inject
    Logger logger;

    @RestClient
    LocationClientV2 locationClientV2;

    @Override
    public List<LocationResponseDTOV2> findLocations(Integer masterCode, String name, String stateName) {
        try {
            return locationClientV2.findLocations(masterCode, name, stateName);


        } catch (Exception ex) {
            FunctionUtil.printLoggerError(logger, nameClass.concat(".findLocationsV2"),
                    ex.getMessage(), new Object() {
                    }.getClass().getEnclosingMethod().getName(),
                    FIND_LOCATIONS);

            return Collections.emptyList();
        }
    }
}
