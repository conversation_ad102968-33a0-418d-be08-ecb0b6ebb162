package com.cvccorp.bff.proxy.dto.promotion.request;

import com.cvccorp.bff.proxy.dto.promotion.AvailableItemDTO;
import com.cvccorp.bff.proxy.dto.promotion.SelectedItemDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class OpportunityRequestDTO {

	private List<AvailableItemDTO> availableItems = Collections.emptyList();
	private List<SelectedItemDTO> selectedItems = Collections.emptyList();
	private OpportunityParamDTO params;

}
