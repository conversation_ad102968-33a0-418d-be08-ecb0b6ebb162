package com.cvccorp.bff.infra.util;

public class AppContext {

    private static Boolean isAuthWithNewToken;
    private static UserContext user;

    public static UserContext getUser() {
        return user;
    }

    public static void setUser(UserContext user) {
        AppContext.user = user;
    }

    public static void newUser(){
        user = new UserContext();
    }

    public static Boolean getIsAuthWithNewToken() {
        return isAuthWithNewToken;
    }

    public static void setIsAuthWithNewToken(Boolean isAuthWithNewToken) {
        AppContext.isAuthWithNewToken = isAuthWithNewToken;
    }
}
