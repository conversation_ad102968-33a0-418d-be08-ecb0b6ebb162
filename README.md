[![Build status](http://corp-jenkins01.compute.br-sao-1.cvccorp.cloud:8080/job/MS/job/MS-bff-java-shopping-busing-dymanic-store/badge/icon)] (<http://corp-jenkins01.compute.br-sao-1.cvccorp.cloud:8080/job/MS/job/MS-bff-java-shopping-busing-dymanic-store/>)
[![Quality Gate](https://sonar.app.cvc.com.br/api/project_badges/measure?project=bff-java-shopping-busing-dymanic-store&metric=alert_status)](https://sonar.app.cvc.com.br/dashboard?id=bff-java-shopping-busing-dymanic-store)
[![Coverage](https://sonar.app.cvc.com.br/api/project_badges/measure?project=bff-java-shopping-busing-dymanic-store&metric=coverage)](https://sonar.app.cvc.com.br/component_measures?id=bff-java-shopping-busing-dymanic-store&metric=Coverage)
[![Maintainnability](https://sonar.app.cvc.com.br/api/project_badges/measure?project=bff-java-shopping-busing-dymanic-store&metric=sqale_rating)] (<https://sonar.app.cvc.com.br/component_measures?id=bff-java-shopping-busing-dymanic-store&metric=Maintainability>)
[![Security](https://sonar.app.cvc.com.br/api/project_badges/measure?project=bff-java-shopping-busing-dymanic-store&metric=security_rating)] (<https://sonar.app.cvc.com.br/component_measures?id=bff-java-shopping-busing-dymanic-store&metric=Security>)

# {bff-java-shopping-busing-dymanic-store}

{DESCRIÇÃO DA APLICAÇÃO}

## {ARQUITETURA}

{DESENHO DA ARQUITETURA}

## {INTEGRAÇÕES DA APLICAÇÃO(LISTAR TODAS AS INTEGRAÇÕES DA APLICAÇÃO)}

### {INTEGRAÇÃO1}

{EXPLICAR SOBRE INTEGRAÇÃO}

### {INTEGRAÇÃO2}

{EXPLICAR SOBRE INTEGRAÇÃO}

## Teste Unitário

Execute a linha de comando:

```sh

```

## Swagger

- {URL DA APLICAÇÃO}

## {ENDPOINTS}

- TI: {ENDPOINT DE TI}
- Lista
- QA: {ENDPOINT DE QA}
- Lista
- PROD: {ENDPOINT DE PROD}
- Lista

Contexto: {NOME DA APLICAÇÃO}

## {RODAR A APLICAÇÃO}

Instale o plugin do Quarkus no IntelliJ IDEA.

Dentro do arquivo `application.properties` já existem algumas configurações para o ambiente de desenvolvimento/produção.

Na IDE, clique em editar configurações e adicione a seguinte linha no campo "profile": `local-prod` para produção ou `dev` para desenvolvimento.

## Query para logs no Kibana

<https://kibana.services.cvc.com.br/app/kibana#/discover?_g=()&_a=(columns:!(message),index:'5725b180-ba9d-11e8-be0f-396272e87c50',interval:auto,query:(language:kuery,query:'kubernetes.namespace_name:%20%22bff-java-shopping-busing-dymanic-store%22%20'),sort:!('@timestamp',desc))>

## {MATRIZ DE RESILIÊNCIA}

{COLOCAR O DESENHO DA MATRIZ DE RESILIÊNCIA (Ex essa abaixo)}
<http://git.cvc.com.br//shopping/road/bff-java-shopping-busing-dymanic-store/README.md>

# bff-java-shopping-core Project

This project uses Quarkus, the Supersonic Subatomic Java Framework.

If you want to learn more about Quarkus, please visit its website: <https://quarkus.io/> .

## Running the application in dev mode

You can run your application in dev mode that enables live coding using:

```shell script
./mvnw compile quarkus:dev
```

> **_NOTE:_** Quarkus now ships with a Dev UI, which is available in dev mode only at <http://localhost:8080/q/dev/>.

## Packaging and running the application

The application can be packaged using:

```shell script
./mvnw package
```

It produces the `quarkus-run.jar` file in the `target/quarkus-app/` directory.
Be aware that it’s not an _über-jar_ as the dependencies are copied into the `target/quarkus-app/lib/` directory.

The application is now runnable using `java -jar target/quarkus-app/quarkus-run.jar`.

If you want to build an _über-jar_, execute the following command:

```shell script
./mvnw package -Dquarkus.package.type=uber-jar
```

The application, packaged as an _über-jar_, is now runnable using `java -jar target/*-runner.jar`.

## Creating a native executable

You can create a native executable using:

```shell script
./mvnw package -Pnative
```

Or, if you don't have GraalVM installed, you can run the native executable build in a container using:

```shell script
./mvnw package -Pnative -Dquarkus.native.container-build=true
```

You can then execute your native executable with: `./target/bff-java-shopping-core-1.0.0-SNAPSHOT-runner`

If you want to learn more about building native executables, please consult <https://quarkus.io/guides/maven-tooling>.

## Provided Code

### RESTEasy Reactive

Easily start your Reactive RESTful Web Services

[Related guide section...](https://quarkus.io/guides/getting-started-reactive#reactive-jax-rs-resources)
