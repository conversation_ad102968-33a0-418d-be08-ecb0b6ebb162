package com.cvccorp.bff.web;

import com.cvccorp.bff.proxy.dto.location.LocationResponseDTOV2;
import com.cvccorp.bff.proxy.interfaces.LocationServiceV2;

import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Path("/v2/cvc/road")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class LocationControllerV2 {


    @Inject
    LocationServiceV2 locationService;

    @GET
    @Path("/locations")
    public List<LocationResponseDTOV2> findLocations(@QueryParam("masterCode") Integer masterCode,
                                                     @QueryParam("name") String name,
                                                     @QueryParam("stateName") String stateName) {

        return locationService.findLocations(masterCode, name, stateName);
    }

}
