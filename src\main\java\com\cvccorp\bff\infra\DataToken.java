package com.cvccorp.bff.infra;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class DataToken {

	private Credential credential;

	@Getter
	@Setter
	@NoArgsConstructor
	public static class Credential {

		private Long personId;
		private Long userId;
		private String name;
		private String email;
		private String cpf;
		private Long branchId;
		private String agentSign;
		private String user;
		private String userType;
	}

}