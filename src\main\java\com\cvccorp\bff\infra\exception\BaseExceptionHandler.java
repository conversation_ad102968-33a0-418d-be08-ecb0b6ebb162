package com.cvccorp.bff.infra.exception;

import org.jboss.logging.Logger;

import javax.inject.Inject;
import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import javax.ws.rs.ext.Provider;
import java.time.LocalDateTime;

@Provider
public class BaseExceptionHandler implements ExceptionMapper<Exception> {

    @Inject
    Logger logger;

    @Override
    public Response toResponse(Exception exception) {
        logger.error(exception);

        BusinessException businessException;
        if(exception instanceof BusinessException){
            businessException = (BusinessException) exception;
        }else {
            businessException = new BusinessException(Response.Status.BAD_REQUEST, "Falha na solicitação");
        }

        MessageException exceptionDTO = MessageException.builder()
                .transactionId(businessException.getTransactionId())
                .timestamp(LocalDateTime.now())
                .status(businessException.getStatus())
                .message(businessException.getMessage())
                .build();

        return Response.status(exceptionDTO.getStatus()).entity(exceptionDTO).build();
    }

}
