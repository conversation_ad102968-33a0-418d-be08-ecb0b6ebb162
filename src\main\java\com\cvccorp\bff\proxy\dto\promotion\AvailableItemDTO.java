package com.cvccorp.bff.proxy.dto.promotion;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
public class AvailableItemDTO {
    private Integer rph;
    private String rateToken;
    private Boolean hasCombo;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PromotionDTO promotion;

    public AvailableItemDTO(Integer rph, String rateToken) {
        this.rph = rph;
        this.rateToken = rateToken;
    }
}
