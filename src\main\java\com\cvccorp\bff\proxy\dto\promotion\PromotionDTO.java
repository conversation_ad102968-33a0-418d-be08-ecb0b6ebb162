package com.cvccorp.bff.proxy.dto.promotion;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Getter
@NoArgsConstructor
public class PromotionDTO {
    private BigDecimal priceWithTax;
    private BigDecimal priceWithoutTax;
    private BigDecimal pricePerDayWithTax;
    private BigDecimal pricePerDayWithoutTax;
    private Double percentage;
    private String discountApplied;
    private List<PromoStatementTO> statements;
    private List<PromoIdTO> promoIds;

    @Getter
    @NoArgsConstructor
    public static class PromoStatementTO {
        private String code;
        private String name;
        private String label; 
        private String type;
        private String discount;
    }

    @Getter
    @NoArgsConstructor
    public static class PromoIdTO {
        private String id;
    }


}
