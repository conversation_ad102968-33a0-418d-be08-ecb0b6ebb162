apiVersion: v1
kind: ConfigMap
metadata:
  name: bff-java-shopping-busing-dymanic-store
  namespace: corp-shopping-busing
data:
  QUARKUS_PROFILE: ti
  ADDITIONAL_OPTS: " "
  PREFIX: bff-java-shopping-busing-dymanic-store
  VAULT_HOST: vault-dev.services.cvc.com.br
  VAULT_PORT: "8200"
  VAULT_SCHEME: http
  CONSUL_HOST: consul-dev.services.cvc.com.br
  CONSUL_PORT: "8500"
  LOGSTASH_HOST: logstash-ti.services.cvc.com.br
  GETWAY_ROAD_URL: https://gtw-road.k8s-qa-cvc.com.br
  LOCATIONS_URL: http://gtw-road-services.k8s-ti-cvc.com.br
  LOCATIONS_V2_URL: https://api-java-quarkus-road-location.k8s-ti-cvc.com.br
  API_OPPORTUNITIES: https://api-opportunities.k8s-ti-cvc.com.br