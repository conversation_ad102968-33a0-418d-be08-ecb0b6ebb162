package com.cvccorp.bff.infra.filters;

import com.cvccorp.bff.infra.exception.BusinessException;
import com.cvccorp.bff.infra.util.AppContext;
import com.cvccorp.bff.infra.util.FunctionUtil;
import com.cvccorp.bff.infra.util.UserContext;
import org.jboss.logging.Logger;

import javax.inject.Inject;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import javax.ws.rs.container.PreMatching;
import javax.ws.rs.core.Response;
import javax.ws.rs.ext.Provider;

import static com.cvccorp.bff.infra.util.Operations.*;

/**
 * Obs. "@PreMatching" garante sempre que o mesmo seja executado antes de qualquer outro filtro
 */

@Provider
@PreMatching
public class RequestFilter implements ContainerRequestFilter {
    private final String nameClass = this.getClass().getSimpleName();

    @Inject
    Logger logger;

    @Override
    public void filter(ContainerRequestContext requestContext) throws BusinessException {
        String transactionId = getTransactioIdHeader(requestContext);
        String token = getTokenHeader(requestContext);
        try{
            AppContext.setUser(new UserContext(
                    token,
                    transactionId,
                    FunctionUtil.decodeToken(token)
            ));
            AppContext.setIsAuthWithNewToken(Boolean.parseBoolean(requestContext.getHeaderString("Auth-New-Token")));
        }catch (Exception e){
            FunctionUtil.printLoggerError(logger, nameClass.concat(".filter"),
                    "Erro ao processar dados da RequestFilter",
                    new Object() {} .getClass().getEnclosingMethod().getName(),
                    FILTER);
            throw new BusinessException(Response.Status.SERVICE_UNAVAILABLE, "Serviço temporariamente indisponível");
        }
    }

    private String getTokenHeader(ContainerRequestContext requestContext){
        String token = requestContext.getHeaderString("Access-Token");
        if(token == null || token.trim().isEmpty()){
            FunctionUtil.printLoggerError(logger, nameClass.concat(".getTokenHeader"),
                    "Token não informado no header",
                    new Object() {} .getClass().getEnclosingMethod().getName(),
                    GET_TOKEN_HEADER);
            throw new BusinessException(Response.Status.BAD_REQUEST, "Token não informado");
        }
        return token;
    }

    private String getTransactioIdHeader(ContainerRequestContext requestContext){
        String transactionId = requestContext.getHeaderString("transaction-id");
        if(transactionId == null || transactionId.trim().isEmpty()){
            FunctionUtil.printLoggerError(logger, nameClass.concat(".getTransactioIdHeader"),
                    "TransactionId não informado no header",
                    new Object() {} .getClass().getEnclosingMethod().getName(),
                    GET_TRANSACTION_ID_HEADER);
            throw new BusinessException(Response.Status.BAD_REQUEST, "TransactionId não informado");
        }
        return transactionId;
    }
}
