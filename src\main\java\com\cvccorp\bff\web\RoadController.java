package com.cvccorp.bff.web;

import com.cvccorp.bff.proxy.dto.road.RoadAvailTablePriceResponseDTO;
import com.cvccorp.bff.proxy.dto.road.grouped.RoadGroupedResponseDTO;
import com.cvccorp.bff.proxy.dto.road.request.RoadFilterRequestDTO;
import com.cvccorp.bff.proxy.dto.road.request.RoadTablePriceFilterRequestDTO;
import com.cvccorp.bff.proxy.dto.road.response.RoadListResponseDTO;
import com.cvccorp.bff.proxy.interfaces.RoadFilterService;
import com.cvccorp.bff.proxy.interfaces.RoadTablePriceService;
import org.jboss.logging.Logger;

import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Path("/v1/road")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class RoadController {

    @Inject
    Logger logger;

    @Inject
    RoadFilterService roadFilterService;

    @Inject
    RoadTablePriceService roadTablePriceService;

    @POST
    public RoadListResponseDTO filter(@NotNull @Valid RoadFilterRequestDTO roadFilter) {
        return roadFilterService.findRoadsByFilter(roadFilter);
    }

    @POST
    @Path("/filter/grouped")
    public RoadGroupedResponseDTO groupedSupllier(@NotNull @Valid RoadFilterRequestDTO roadFilter) {
        return roadFilterService.findRoadsByFilterGrouped(roadFilter);
    }

    @GET
    @Path("/table-price")
    public List<RoadAvailTablePriceResponseDTO> findRoadsTablePrice(
            @Valid @BeanParam RoadTablePriceFilterRequestDTO filter) {
        return roadTablePriceService.findRoadsTablePrice(filter);
    }

}
