package com.cvccorp.bff.service.promotion;

import com.cvccorp.bff.infra.util.FunctionUtil;
import com.cvccorp.bff.proxy.dto.promotion.request.OpportunityRequestDTO;
import com.cvccorp.bff.proxy.dto.promotion.response.OpportunitiesResponseDTO;
import com.cvccorp.bff.proxy.interfaces.PromotionService;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

import static com.cvccorp.bff.infra.util.Operations.GET_OPORTUNITIES;

@RequestScoped
public class PromotionServiceImpl implements PromotionService {
    private final String nameClass = this.getClass().getSuperclass().getSimpleName();

    @Inject
    Logger logger;

    @Inject
    ObjectMapper objectMapper;

    @RestClient
    PromotionClient promotionClient;

    @Override
    public OpportunitiesResponseDTO getOpportunities(OpportunityRequestDTO body){
        FunctionUtil.printLoggerInfo(logger, nameClass.concat(".getOpportunities"),
                "Iniciando busca de oportunidades",
                new Object() {} .getClass().getEnclosingMethod().getName(), GET_OPORTUNITIES);
        try {
            objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            return promotionClient.opportunities(objectMapper.writeValueAsString(body));
        } catch (Exception ex) {
            FunctionUtil.printLoggerError(logger, nameClass.concat(".getOpportunities"),
                    ex.getMessage(),
                    new Object() {} .getClass().getEnclosingMethod().getName(),
                    GET_OPORTUNITIES);
            return null;
        }
    }

}
