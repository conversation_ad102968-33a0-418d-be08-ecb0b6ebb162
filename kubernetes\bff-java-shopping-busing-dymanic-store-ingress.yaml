apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: bff-java-shopping-busing-dymanic-store-ingress
  namespace: corp-shopping-busing
  annotations:
    nginx.org/ssl-backends: "bff-java-shopping-busing-dymanic-store-service"
spec:
  ingressClassName: "nginx-private"
  rules:
    - host: bff-java-shopping-busing-dymanic-store.__SERVICE_ENV__-cvc.com.br
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: bff-java-shopping-busing-dymanic-store-service
                port:
                  number: 8080
