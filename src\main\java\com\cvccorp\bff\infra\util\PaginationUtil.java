package com.cvccorp.bff.infra.util;

import com.cvccorp.bff.proxy.dto.road.PaginationInfoDTO;

import java.util.Collections;
import java.util.List;

public class PaginationUtil {

    public static <T> List<T> applyPagination(List<T> trips, PaginationInfoDTO paginationInfoDTO) {
        if(paginationInfoDTO.getPageSize() <= 0 || paginationInfoDTO.getCurPage() <= 0) {
            throw new IllegalArgumentException("PageSize inválido " + paginationInfoDTO.getPageSize());
        }
        int fromIndex = (paginationInfoDTO.getCurPage() - 1) * paginationInfoDTO.getPageSize();
        if(trips == null || trips.size() < fromIndex){
            return Collections.emptyList();
        }
        return trips.subList(fromIndex, Math.min(fromIndex + paginationInfoDTO.getPageSize(), trips.size()));
    }
}
