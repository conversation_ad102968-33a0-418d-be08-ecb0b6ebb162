package com.cvccorp.bff.web;

import com.cvccorp.bff.proxy.dto.location.LocationResponseDTO;
import com.cvccorp.bff.proxy.interfaces.LocationService;
import org.jboss.logging.Logger;

import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Path("/v1/cvc/road")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class LocationController {

    @Inject
    Logger logger;

    @Inject
    LocationService locationService;

    @GET
    @Path("/locations")
    public List<LocationResponseDTO> findLocations(@QueryParam("quant") Integer quant, @QueryParam("name") String name) {
        return locationService.findLocations(quant, name);
    }
    
}
