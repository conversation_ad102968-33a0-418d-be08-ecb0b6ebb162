package com.cvccorp.bff.service.location;

import com.cvccorp.bff.infra.exception.ClientExceptionMapper;
import com.cvccorp.bff.infra.filters.RequestHeadersFactory;
import com.cvccorp.bff.proxy.dto.location.LocationResponseDTO;
import org.eclipse.microprofile.rest.client.annotation.RegisterClientHeaders;
import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;

@RegisterRestClient(configKey = "rest-client-gatway-location")
@RegisterClientHeaders(RequestHeadersFactory.class)
@RegisterProvider(ClientExceptionMapper.class)
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public interface LocationClient {

    @GET
    @Path("/road/cvc/location/")
    List<LocationResponseDTO> findLocations();

}
