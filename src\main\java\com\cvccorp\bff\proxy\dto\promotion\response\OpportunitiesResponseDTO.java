package com.cvccorp.bff.proxy.dto.promotion.response;

import com.cvccorp.bff.proxy.dto.promotion.AvailableItemDTO;
import com.cvccorp.bff.proxy.dto.promotion.SelectedItemDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Getter
@NoArgsConstructor
public class OpportunitiesResponseDTO {

	private final List<AvailableItemDTO> availableItems = Collections.emptyList();
	private final List<SelectedItemDTO> selectedItems = Collections.emptyList();
	private Map<String, Object> meta;
}
