package com.cvccorp.bff.web;

import com.cvccorp.bff.proxy.dto.road.SeatBlockDTO;
import com.cvccorp.bff.proxy.dto.road.response.SeatBlockResponseDTO;
import com.cvccorp.bff.proxy.dto.road.response.SeatResponseDTO;
import com.cvccorp.bff.proxy.interfaces.SeatService;
import io.smallrye.mutiny.Uni;
import org.jboss.logging.Logger;

import javax.inject.Inject;
import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

@Path("/v1/road")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class SeatController {

    @Inject
    Logger logger;

    @Inject
    SeatService seatService;

    @GET
    @Path("/seats/{tripToken}")
    public Uni<SeatResponseDTO> seats(@PathParam("tripToken") String tripToken) {
        return seatService.seats(tripToken);
    }

    @POST
    @Path("/seat")
    public Uni<SeatBlockResponseDTO> reservationSeats(@Valid SeatBlockDTO seatBlockRequestTO) {
        return seatService.reservationSeats(seatBlockRequestTO);
    }

}
