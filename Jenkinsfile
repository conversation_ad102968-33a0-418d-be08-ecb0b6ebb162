@Library('cvc-jen<PERSON>-lib')

final def projectConfig = this.readJSON(text: """
{
    "name": "bff-java-shopping-busing-dymanic-store",
    "git": {
        "repositoryUrl": "******************:/shopping/road/bff-java-shopping-busing-dymanic-store.git"
    },
    "technology": {
        "name": "JAVA",
        "dependencyManager": "MAVEN",
        "version": "11",
        "buildCommands": {
            "buildApp": "clean package"
        }
    },
    "docker": {
        "dockerfilePath": "Dockerfile"
    },
    "kubernetes": {
        "namespace": "corp-shopping-busing"
    }
}
""")

final commons = cvcCorpPipeline.getCommons(this, projectConfig)

pipeline {
    agent any
    stages {
        stage('Deploy TI') {
            steps {
                script {
                    deploy(this, 'TI', projectConfig, commons) {
                        
                    }
                }
            }
        }
        stage('Deploy QA') {
            steps {
                script {
                    deploy(this, 'QA', projectConfig, commons) {

                    }
                }
            }
        }
        stage('Deploy PROD') {
            steps {
                script {
                    deploy(this, 'PROD', projectConfig, commons) {

                    }
                }
            }
        }
    }
    post {
        always {
            dir("${this.env.WORKSPACE}") {
                //Clean Workspace
                deleteDir()
            }
        }
    }
}