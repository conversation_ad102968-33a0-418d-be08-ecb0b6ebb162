package com.cvccorp.bff.service.location;

import com.cvccorp.bff.infra.exception.ClientExceptionMapper;
import com.cvccorp.bff.infra.filters.RequestHeadersFactory;
import com.cvccorp.bff.proxy.dto.location.LocationResponseDTO;
import com.cvccorp.bff.proxy.dto.location.LocationResponseDTOV2;
import org.eclipse.microprofile.rest.client.annotation.RegisterClientHeaders;
import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import java.util.List;

@RegisterRestClient(configKey = "rest-client-gatway-location-v2")
@RegisterClientHeaders(RequestHeadersFactory.class)
@RegisterProvider(ClientExceptionMapper.class)
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public interface LocationClientV2 {

    @GET
    @Path("/api/v1/road/locations")
    List<LocationResponseDTOV2> findLocations(@QueryParam("masterCode") Integer masterCode,
                                              @QueryParam("name") String name,
                                              @QueryParam("stateName") String stateName);

}
