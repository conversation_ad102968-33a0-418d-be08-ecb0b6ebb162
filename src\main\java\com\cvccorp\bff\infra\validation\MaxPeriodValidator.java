package com.cvccorp.bff.infra.validation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

public class MaxPeriodValidator implements ConstraintValidator<MaxPeriod, LocalDate> {

    private int maxDays;

    @Override
    public void initialize(MaxPeriod constraint) {
        this.maxDays = constraint.maxDays();
    }

    @Override
    public boolean isValid(LocalDate value, ConstraintValidatorContext ctx) {
        LocalDate currentDate = LocalDate.now();
        long diff = ChronoUnit.DAYS.between(currentDate, value);

        return diff <= maxDays;
    }
}
