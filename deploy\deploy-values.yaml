environment:
  - name: TI
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 800Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /q/health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /q/health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-java-shopping-busing-dymanic-store
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: CONSUL_PORT
                value: "8500"
              - name: LOGSTASH_HOST
                value: logstash-ti.services.cvc.com.br
              - name: GETWAY_ROAD_URL
                value: https://gtw-road.k8s-qa-cvc.com.br
              - name: API_OPPORTUNITIES
                value: https://api-opportunities.k8s-ti-cvc.com.br
              - name: PREFIX
                value: bff-java-shopping-busing-dymanic-store
              - name: CONSUL_HOST
                value: consul-dev.services.cvc.com.br
              - name: VAULT_HOST
                value: vault-dev.services.cvc.com.br
              - name: VAULT_PORT
                value: "8200"
              - name: VAULT_SCHEME
                value: http
              - name: LOCATIONS_URL
                value: http://gtw-road-services.k8s-ti-cvc.com.br
              - name: QUARKUS_PROFILE
                value: ti
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: LOCATIONS_V2_URL
                value: https://api-java-quarkus-road-location.k8s-ti-cvc.com.br
  - name: QA-TMP
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 800Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /q/health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /q/health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-java-shopping-busing-dymanic-store
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: GETWAY_ROAD_URL
                value: https://gtw-road.k8s-qa-cvc.com.br
              - name: LOCATIONS_URL
                value: http://gtw-road-services.k8s-qa-cvc.com.br
              - name: VAULT_HOST
                value: vault.qa.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: LOGSTASH_HOST
                value: logstash-qa.services.cvc.com.br
              - name: VAULT_PORT
                value: "8200"
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: API_OPPORTUNITIES
                value: https://api-opportunities.k8s-qa-cvc.com.br
              - name: QUARKUS_PROFILE
                value: qa
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: PREFIX
                value: bff-java-shopping-busing-dymanic-store
              - name: LOCATIONS_V2_URL
                value: https://api-java-quarkus-road-location.k8s-qa-cvc.com.br
  - name: QA
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 800Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /q/health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /q/health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-java-shopping-busing-dymanic-store
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: GETWAY_ROAD_URL
                value: https://gtw-road.k8s-qa-cvc.com.br
              - name: LOCATIONS_URL
                value: http://gtw-road-services.k8s-qa-cvc.com.br
              - name: VAULT_HOST
                value: vault.qa.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: LOGSTASH_HOST
                value: logstash-qa.services.cvc.com.br
              - name: VAULT_PORT
                value: "8200"
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: API_OPPORTUNITIES
                value: https://api-opportunities.k8s-qa-cvc.com.br
              - name: QUARKUS_PROFILE
                value: qa
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: PREFIX
                value: bff-java-shopping-busing-dymanic-store
              - name: LOCATIONS_V2_URL
                value: https://api-java-quarkus-road-location.k8s-qa-cvc.com.br
  - name: PILOT
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 800Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /q/health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /q/health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-java-shopping-busing-dymanic-store
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: QUARKUS_PROFILE
                value: prod
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: VAULT_PORT
                value: "8200"
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: LOGSTASH_HOST
                value: logstash.services.cvc.com.br
              - name: GETWAY_ROAD_URL
                value: https://gtw-road.k8s-cvc.com.br
              - name: API_OPPORTUNITIES
                value: https://api-opportunities.k8s-cvc.com.br
              - name: PREFIX
                value: bff-java-shopping-busing-dymanic-store
              - name: VAULT_HOST
                value: vault.prod.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: LOCATIONS_URL
                value: http://gtw-road-services.k8s-cvc.com.br
              - name: LOCATIONS_V2_URL
                value: https://api-java-quarkus-road-location.k8s-cvc.com.br
  - name: PROD
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 800Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /q/health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /q/health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-java-shopping-busing-dymanic-store
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: QUARKUS_PROFILE
                value: prod
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: VAULT_PORT
                value: "8200"
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: LOGSTASH_HOST
                value: logstash.services.cvc.com.br
              - name: GETWAY_ROAD_URL
                value: https://gtw-road.k8s-cvc.com.br
              - name: API_OPPORTUNITIES
                value: https://api-opportunities.k8s-cvc.com.br
              - name: PREFIX
                value: bff-java-shopping-busing-dymanic-store
              - name: VAULT_HOST
                value: vault.prod.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: LOCATIONS_URL
                value: http://gtw-road-services.k8s-cvc.com.br
              - name: LOCATIONS_V2_URL
                value: https://api-java-quarkus-road-location.k8s-cvc.com.br
