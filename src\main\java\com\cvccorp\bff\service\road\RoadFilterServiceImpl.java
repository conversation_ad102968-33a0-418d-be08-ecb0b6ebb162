package com.cvccorp.bff.service.road;

import com.cvccorp.bff.infra.util.FunctionUtil;
import com.cvccorp.bff.proxy.dto.promotion.AvailableItemDTO;
import com.cvccorp.bff.proxy.dto.promotion.request.OpportunityParamDTO;
import com.cvccorp.bff.proxy.dto.promotion.request.OpportunityRequestDTO;
import com.cvccorp.bff.proxy.dto.promotion.response.OpportunitiesResponseDTO;
import com.cvccorp.bff.proxy.dto.road.PaginationInfoDTO;
import com.cvccorp.bff.proxy.dto.road.RoadTripDTO;
import com.cvccorp.bff.proxy.dto.road.grouped.RoadGroupedLocationDTO;
import com.cvccorp.bff.proxy.dto.road.grouped.RoadGroupedResponseDTO;
import com.cvccorp.bff.proxy.dto.road.grouped.RoadGroupedSupplierDTO;
import com.cvccorp.bff.proxy.dto.road.grouped.RoadGroupedTripDTO;
import com.cvccorp.bff.proxy.dto.road.request.RoadFilterRequestDTO;
import com.cvccorp.bff.proxy.dto.road.response.RoadListResponseDTO;
import com.cvccorp.bff.proxy.interfaces.PromotionService;
import com.cvccorp.bff.proxy.interfaces.RoadFilterService;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.cvccorp.bff.infra.util.Operations.*;
import static com.cvccorp.bff.infra.util.PaginationUtil.applyPagination;
import static java.util.Objects.nonNull;


@RequestScoped
public class RoadFilterServiceImpl implements RoadFilterService {
    private final String nameClass = this.getClass().getSuperclass().getSimpleName();

    private static final String PROFILE_NAME = "rod-avail";

    @Inject
    Logger logger;

    @RestClient
    RoadClient gtwRoadClient;

    @Inject
    PromotionService promotionService;

    @Override
    public RoadListResponseDTO findRoadsByFilter(RoadFilterRequestDTO filter) {
        FunctionUtil.printLoggerInfo(logger, nameClass.concat(".findRoadsByFilter"), "Iniciando busca de Roads",
                new Object() {} .getClass().getEnclosingMethod().getName(), FILTER);
        try {
            RoadListResponseDTO roadListResponse = gtwRoadClient.roads(filter);
            joinPromotion(roadListResponse.getTrips(), filter);
            return roadListResponse;
        } catch (Exception ex) {
            FunctionUtil.printLoggerError(logger, nameClass.concat(".findRoadsByFilter"),
                    ex.getMessage(),
                    new Object() {} .getClass().getEnclosingMethod().getName(),
                    FILTER);
            return new RoadListResponseDTO();
        }
    }

    @Override
    public RoadGroupedResponseDTO findRoadsByFilterGrouped(RoadFilterRequestDTO filter) {
        FunctionUtil.printLoggerInfo(logger, nameClass.concat(".findRoadsByFilterGrouped"), "Iniciando agrupamento por supplier.",
                new Object() {} .getClass().getEnclosingMethod().getName(), AGROUP);
        try {
            Integer pageSize = filter.getPageSize();
            Integer curPage = filter.getCurPage();
            filter.setPageSize(1000);
            RoadListResponseDTO roadListResponse = gtwRoadClient.roads(filter);
            joinPromotion(roadListResponse.getTrips(), filter);
            PaginationInfoDTO paginationInfoDTO = new PaginationInfoDTO();
            if(pageSize != null && curPage != null) {
                paginationInfoDTO.setCurPage(curPage);
                paginationInfoDTO.setPageSize(pageSize);
                roadListResponse.getMeta().getPaging().setPageSize(pageSize);
                roadListResponse.getMeta().getPaging().setCurPage(curPage);
            } else {
                paginationInfoDTO.setCurPage(1);
                paginationInfoDTO.setPageSize(1000);
            }
            return populateRoadtripResponse(roadListResponse, paginationInfoDTO);
        } catch (Exception ex) {
            FunctionUtil.printLoggerError(logger, nameClass.concat(".findRoadsByFilterGrouped"),
                    ex.getMessage(),
                    new Object() {} .getClass().getEnclosingMethod().getName(),
                    AGROUP);
            return new RoadGroupedResponseDTO();
        }
    }

    private void joinPromotion(List<RoadTripDTO> trips, RoadFilterRequestDTO filter) {
        FunctionUtil.printLoggerInfo(logger, nameClass.concat(".joinPromotion"), "Iniciando join promotions.",
                new Object() {} .getClass().getEnclosingMethod().getName(), JOIN_PROMOTION);
        try {
            List<AvailableItemDTO> availableItemTOList = new ArrayList<>();
            Map<Integer, RoadTripDTO> roads = new HashMap<>();
            int rph = 1;
            for (RoadTripDTO trip : trips) {
                roads.put(rph, trip);
                availableItemTOList.add(new AvailableItemDTO(rph, trip.getRateToken()));
                rph++;
            }

            OpportunityRequestDTO opportunityRequestTO = new OpportunityRequestDTO();
            opportunityRequestTO.setAvailableItems(availableItemTOList);
            opportunityRequestTO.setSelectedItems(filter.getPromoSelectedItems());
            if (filter.getPromoOpportunityParam() == null) {
                filter.setPromoOpportunityParam(new OpportunityParamDTO());
            }
            filter.getPromoOpportunityParam().setProfileName(PROFILE_NAME);
            opportunityRequestTO.setParams(filter.getPromoOpportunityParam());

            OpportunitiesResponseDTO response = promotionService.getOpportunities(opportunityRequestTO);
            response.getAvailableItems().forEach(item -> {
                if (nonNull(roads.get(item.getRph()))) {
                    roads.get(item.getRph()).setPromotion(item.getPromotion());
                    roads.get(item.getRph()).setHasCombo(item.getHasCombo());
                }
            });
        } catch (Exception ex) {
            FunctionUtil.printLoggerError(logger, nameClass.concat(".joinPromotion"),
                    ex.getMessage(),
                    new Object() {} .getClass().getEnclosingMethod().getName(),
                    JOIN_PROMOTION);
        }
    }

    /**
     * @param roadListResponseDTO response vindo do gateway
     * @return retorna um objeto com as trips agrupadas
     */
    private RoadGroupedResponseDTO populateRoadtripResponse(RoadListResponseDTO roadListResponseDTO, PaginationInfoDTO paginationInfoDTO) {
        RoadGroupedResponseDTO roadGroupedResponseDTO = new RoadGroupedResponseDTO();
        roadGroupedResponseDTO.setMeta(roadListResponseDTO.getMeta());
        List<RoadGroupedTripDTO> roadGroupedTrips = new ArrayList<>();

        for (RoadTripDTO tripResponse : roadListResponseDTO.getTrips()) {

            Optional<RoadGroupedTripDTO> optional = roadGroupedTrips.stream()
                    .filter(item -> item.getSupplier().getName().equals(tripResponse.getSupplier().getName()))
                    .filter(item -> item.getServiceClass().equals(tripResponse.getServiceClass()))
                    .filter(item -> item.getPriceWithoutTax().equals(tripResponse.getPriceWithoutTax()))
                    .findFirst();

            if(optional.isEmpty()) {
                RoadGroupedTripDTO roadGroupedTrip = new RoadGroupedTripDTO();
                roadGroupedTrip.setTripId(tripResponse.getTripId());
                roadGroupedTrip.setServiceClass(tripResponse.getServiceClass());
                roadGroupedTrip.setPriceWithoutTax(tripResponse.getPriceWithoutTax());
                roadGroupedTrip.setSupplier(new RoadGroupedSupplierDTO(
                        tripResponse.getSupplier().getId(),
                        tripResponse.getSupplier().getName(),
                        tripResponse.getSupplier().getNonRefundable(),
                        tripResponse.getSupplier().getSupplierBrokerLogo()
                ));
                roadGroupedTrip.setArrival(new RoadGroupedLocationDTO(
                        tripResponse.getArrival().getLocation().getId(),
                        tripResponse.getArrival().getLocation().getName()
                ));
                roadGroupedTrip.setDeparture(new RoadGroupedLocationDTO(
                        tripResponse.getDeparture().getLocation().getId(),
                        tripResponse.getDeparture().getLocation().getName()
                ));
                roadGroupedTrips.add(roadGroupedTrip);
                roadGroupedTrip.getTimetable().add(tripResponse);
            }else {
                optional.get().getTimetable().add(tripResponse);
            }
        }

        int totalPages = (int) Math.ceil((double) roadGroupedTrips.size() / paginationInfoDTO.getPageSize());
        roadGroupedResponseDTO.getMeta().getPaging().setPageCount(totalPages);
        roadGroupedResponseDTO.getMeta().getPaging().setTotalSize(roadGroupedTrips.size());
        roadGroupedResponseDTO.setTrips(applyPagination(roadGroupedTrips, paginationInfoDTO));
        roadGroupedResponseDTO.setCancelPolicies(roadListResponseDTO.getCancelPolicies());

        return roadGroupedResponseDTO;
    }
}
