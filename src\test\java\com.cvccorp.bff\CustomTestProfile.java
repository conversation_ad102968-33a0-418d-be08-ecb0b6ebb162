package com.cvccorp.bff;

import io.quarkus.test.junit.QuarkusTestProfile;

public class CustomTestProfile implements QuarkusTestProfile {

//    @Override
//    public Map<String, String> getConfigOverrides() {
//        return Collections.singletonMap("quarkus.resteasy.path", "/custom");
//    }

//    @Override
//    public Set<Class<?>> getEnabledAlternatives() {
//        return Collections.singleton(MyRepository.class);
//    }

    @Override
    public String getConfigProfile() {
        return "custom-test-profile";
    }
}
