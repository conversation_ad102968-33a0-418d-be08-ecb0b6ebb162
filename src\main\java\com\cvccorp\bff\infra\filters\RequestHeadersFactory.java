package com.cvccorp.bff.infra.filters;

import com.cvccorp.bff.infra.DataToken;
import com.cvccorp.bff.infra.util.AppContext;
import org.eclipse.microprofile.rest.client.ext.ClientHeadersFactory;

import javax.enterprise.context.ApplicationScoped;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;

@ApplicationScoped
public class RequestHeadersFactory implements ClientHeadersFactory {
    @Override
    public MultivaluedMap<String, String> update(MultivaluedMap<String, String> multivaluedMap, MultivaluedMap<String, String> multivaluedMap1) {
        DataToken.Credential credential = AppContext.getUser().getDataToken().getCredential();
        MultivaluedMap<String, String> headers = new MultivaluedHashMap<>();
        headers.add("gtw-sec-user-token", AppContext.getUser().getToken());
        headers.add("access-new-token", AppContext.getIsAuthWithNewToken().toString());
        headers.add("gtw-username", credential.getUser());
        headers.add("gtw-transaction-id", AppContext.getUser().getTransactionId());
        headers.add("gtw-branch-id", credential.getBranchId().toString());
        headers.add("gtw-agent-sign", credential.getAgentSign());
        return headers;
    }
}
