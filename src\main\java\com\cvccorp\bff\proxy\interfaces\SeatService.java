package com.cvccorp.bff.proxy.interfaces;

import com.cvccorp.bff.proxy.dto.road.SeatBlockDTO;
import com.cvccorp.bff.proxy.dto.road.response.SeatBlockResponseDTO;
import com.cvccorp.bff.proxy.dto.road.response.SeatResponseDTO;
import io.smallrye.mutiny.Uni;

public interface SeatService {
    Uni<SeatResponseDTO> seats(String tripToken);
    Uni<SeatBlockResponseDTO> reservationSeats(SeatBlockDTO seatBlockRequestTO);
}
