quarkus.application.name=dynamic-busing
#quarkus.http.root-path=/api/${quarkus.application.name}
quarkus.default-locale=pt_BR
quarkus.http.cors=true
quarkus.http.enable-compression=true

quarkus.swagger-ui.always-include=true
mp.openapi.extensions.smallrye.info.title=BFF Shopping Dymanic Busing
mp.openapi.extensions.smallrye.info.version=1.0
mp.openapi.extensions.smallrye.info.description=BFF Application Shopping Dymanic Busing
mp.openapi.extensions.smallrye.info.contact.email=<EMAIL>
mp.openapi.extensions.smallrye.info.contact.name=Cvc
mp.openapi.extensions.smallrye.info.license.name=Apache 2.0
mp.openapi.extensions.smallrye.info.license.url=http://www.apache.org/licenses/LICENSE-2.0.html
quarkus.kubernetes.env.configmaps=${quarkus.application.name}

# Clients
rest-client-getway-road/mp-rest/url=${GETWAY_ROAD_URL}
rest-client-gatway-location/mp-rest/url=${LOCATIONS_URL}
rest-client-gatway-location-v2/mp-rest/url=${LOCATIONS_V2_URL}
rest-client-api-opportunities/mp-rest/url=${API_OPPORTUNITIES}

# Logstash
quarkus.log.handler.gelf.enabled=true
quarkus.log.handler.gelf.host=${LOGSTASH_HOST}
quarkus.log.handler.gelf.port=12201
quarkus.log.handler.gelf.level=ALL
quarkus.log.handler.gelf.include-full-mdc=true

#60 segundos
quarkus.rest-client.connect-timeout=60000
quarkus.rest-client.read-timeout=60000

# Dev
%dev.quarkus.log.handler.gelf.host=logstash-qa.services.cvc.com.br
%dev.rest-client-getway-road/mp-rest/url=https://gtw-road.k8s-qa-cvc.com.br
%dev.rest-client-gatway-location/mp-rest/url=http://gtw-road-services.k8s-cvc.com.br:8080
%dev.rest-client-gatway-location-v2/mp-rest/url=https://api-java-quarkus-road-location.k8s-qa-cvc.com.br
%dev.rest-client-api-opportunities/mp-rest/url=https://api-opportunities.k8s-qa-cvc.com.br

# Local Prod
%local-prod.quarkus.log.handler.gelf.host=logstash.services.cvc.com.br
%local-prod.rest-client-getway-road/mp-rest/url=https://gtw-road.k8s-cvc.com.br
%local-prod.rest-client-gatway-location/mp-rest/url=http://gtw-road-services.k8s-cvc.com.br:8080
%local-prod.rest-client-gatway-location-v2/mp-rest/url=https://api-java-quarkus-road-location.k8s-cvc.com.br
%local-prod.rest-client-api-opportunities/mp-rest/url=https://api-opportunities.k8s-cvc.com.br

%local-prod.quarkus.rest-client.logging.scope=request-response
%local-prod.quarkus.rest-client.logging.body-limit=1024
%local-prod.quarkus.log.category."org.jboss.resteasy.reactive.client.logging".level=DEBUG

# Test
#quarkus.http.test-port=9440
#quarkus.http.test-ssl-port=9443
%custom-test-profile.rest-client-getway-road/mp-rest/url=https://gtw-road.k8s-qa-cvc.com.br
%custom-test-profile.rest-client-gatway-location/mp-rest/url=http://gtw-road-services.k8s-qa-cvc.com.br
%custom-test-profile.quarkus.log.handler.gelf.host=logstash-qa.services.cvc.com.br
%custom-test-profile.rest-client-api-opportunities/mp-rest/url=https://api-opportunities.k8s-qa-cvc.com.br
%custom-test-profile.rest-client-gatway-location-v2/mp-rest/url=https://api-java-quarkus-road-location.k8s-qa-cvc.com.br

# ============================================================================
# VeraCode Compatibility: Force Quarkus to create a single uber-jar
# This prevents the "2 JARs" issue that causes Error 126 in VeraCode scan
# ============================================================================
quarkus.package.type=uber-jar