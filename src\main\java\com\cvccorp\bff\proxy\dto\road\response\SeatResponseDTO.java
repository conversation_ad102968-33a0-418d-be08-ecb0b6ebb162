package com.cvccorp.bff.proxy.dto.road.response;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@NoArgsConstructor
public class SeatResponseDTO {

    private List<SeatFloor> floor;

    @Getter
    @NoArgsConstructor
    public static class SeatFloor {

        private List<SeatRow> row;

        @Getter
        @NoArgsConstructor
        public static class SeatRow {

            private List<SeatColumn> column;

            @Getter
            @NoArgsConstructor
            public static class SeatColumn {

                private static enum enumStatus {AVAILABLE, UNAVAILABLE, EMPTY};

                private Integer id;
                private enumStatus status;
                private String label;
            }
        }
    }
}
