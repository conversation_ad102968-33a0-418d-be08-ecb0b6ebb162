package com.cvccorp.bff.infra.util;

import com.cvccorp.bff.infra.DataToken;
import com.cvccorp.bff.infra.exception.BusinessException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.Logger;
import org.slf4j.MDC;

import javax.ws.rs.core.Response;
import java.text.Normalizer;
import java.util.*;

import static com.cvccorp.bff.infra.util.ConstantsLogs.*;

public class FunctionUtil {

    public static boolean compareStringIgnoreCase(String var1, String var2){
        return normalizeString(var1.toLowerCase()).contains(FunctionUtil.normalizeString(var2.toLowerCase()));
    }

    public static String normalizeString(String value){
        return Normalizer.normalize(value, Normalizer.Form.NFKD).replaceAll("[^\\p{ASCII}]", "");
    }

    public static DataToken decodeToken(String token) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            String[] parts = token.split("\\.");
//            String header = decodeBase64(parts[0]);
            String payload = decodeBase64(parts[1]);

            return objectMapper.readValue(payload, DataToken.class);
        } catch (JsonProcessingException e) {
            throw new BusinessException(Response.Status.BAD_REQUEST, e.getMessage());
        }
    }

    private static String decodeBase64(String encodedString) {
        return new String(Base64.getUrlDecoder().decode(encodedString));
    }

    public static String generateTransactionId(){
        return UUID.randomUUID().toString();
    }

    public static boolean empty(Object value){
        if(value == null){
            return true;
        }
        return value instanceof String && ((String) value).trim().isEmpty();
    }

    public static void printLoggerInfo(Logger logger, String pathName, String message, String method, String operation){
        MDC.put(NAMESPACE, "corp-shopping-busing");
        MDC.put(APP_NAME, "bff-java-shopping-busing-dymanic-store");
        MDC.put(OPERATION, operation);
        MDC.put(METHOD, method);
        logger.info(formatMessageLogger(pathName, message));
    }

    public static void printLoggerError(Logger logger, String pathName, String message, String method, String operation){
        MDC.put(NAMESPACE, "corp-shopping-busing");
        MDC.put(APP_NAME, "bff-java-shopping-busing-dymanic-store");
        MDC.put(OPERATION, operation);
        MDC.put(METHOD, method);
        logger.error(formatMessageLogger(pathName, message));
    }

    private static String formatMessageLogger(String pathName, String message){
        return String.format(
                "m:%s, transactionId:%s, message:%s",
                pathName,
                getTransactionIdUserContext(),
                message
        );
    }

    public static String getTransactionIdUserContext(){
        return AppContext.getUser() != null ? AppContext.getUser().getTransactionId() : null;
    }

    public static String getEstado (String uf) {
        List<String> estados = new ArrayList<>(Arrays.asList(
                "MG",
                "AC",
                "AL",
                "AP",
                "AM",
                "BA",
                "CE",
                "DF",
                "ES",
                "GO",
                "MA",
                "MT",
                "MS",
                "PA",
                "PB",
                "PR",
                "PE",
                "PI",
                "RJ",
                "RN",
                "RS",
                "RO",
                "RR",
                "SC",
                "SP",
                "SE",
                "TO"));
        return estados.stream()
                .filter(estado -> estado.equalsIgnoreCase(uf))
                .findFirst()
                .orElse(uf);
    }

    public static String capitalizeDescription(String description) {
        String[] descriptionSplit = description.toLowerCase().split(" ");
        for (int i = 0; i < descriptionSplit.length; i++) {
            if (descriptionSplit[i].length() != 2) {
                descriptionSplit[i] = StringUtils.capitalize(descriptionSplit[i]);
            }else{
                descriptionSplit[i] = getEstado(descriptionSplit[i]);
            }
        }
        return String.join(" ", descriptionSplit);
    }
}
