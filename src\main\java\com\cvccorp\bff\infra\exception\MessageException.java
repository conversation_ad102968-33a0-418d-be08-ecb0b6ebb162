package com.cvccorp.bff.infra.exception;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MessageException {

    private LocalDateTime timestamp;
    private Integer status;
    private String message;
    private String path;
    private String trace;
    private String transactionId;

}
