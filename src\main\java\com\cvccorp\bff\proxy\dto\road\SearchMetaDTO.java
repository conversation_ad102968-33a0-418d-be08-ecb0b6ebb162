package com.cvccorp.bff.proxy.dto.road;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@NoArgsConstructor
public class SearchMetaDTO {
    private FilterInfoDTO filter;
    private List<SearchFilterDTO> timeRanges;
    private List<SearchFilterDTO> companies;
    private List<SearchFilterDTO> serviceClass;
    private List<SearchFilterDTO> departureLocations;
    private List<SearchFilterDTO> arrivalLocations;
    private PriceRangeDTO priceRange;
    private PaginationInfoDTO paging;
}
