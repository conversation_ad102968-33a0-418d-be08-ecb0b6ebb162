package com.cvccorp.bff;

import com.cvccorp.bff.proxy.dto.road.*;
import com.cvccorp.bff.proxy.dto.road.grouped.RoadGroupedLocationDTO;
import com.cvccorp.bff.proxy.dto.road.grouped.RoadGroupedResponseDTO;
import com.cvccorp.bff.proxy.dto.road.grouped.RoadGroupedSupplierDTO;
import com.cvccorp.bff.proxy.dto.road.grouped.RoadGroupedTripDTO;
import com.cvccorp.bff.proxy.dto.road.request.RoadFilterRequestDTO;
import com.cvccorp.bff.proxy.dto.road.response.RoadListResponseDTO;
import com.cvccorp.bff.service.road.RoadClient;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.TestProfile;
import io.quarkus.test.junit.mockito.InjectMock;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;


@QuarkusTest
@TestProfile(CustomTestProfile.class)
public class RoadTest {

    @InjectMock
    RoadClient roadClient;

    @Test
    void groupedToSupplierArrivalDeparture() {

        RoadFilterRequestDTO filter = getRoadFilterResponseDTO();
        RoadGroupedResponseDTO response = getRoadgroupedResponseDTOMock();

        when(roadClient.roads(filter)).thenReturn(new RoadListResponseDTO());

        response.getTrips().forEach(roadTripGroupedDTO -> roadTripGroupedDTO.getTimetable().forEach(roadTripDTO -> {
            if(roadTripDTO.getSupplier().getId().equals(roadTripGroupedDTO.getSupplier().getId()) &&
               roadTripDTO.getArrival().getLocation().getId().equals(roadTripGroupedDTO.getArrival().getId()) &&
               roadTripDTO.getArrival().getLocation().getId().equals(roadTripGroupedDTO.getDeparture().getId())
            ) {
                assertTrue(true);
            } else {
                assertFalse(false);
            }
        }));

    }

    private RoadGroupedResponseDTO getRoadgroupedResponseDTOMock() {
        RoadGroupedResponseDTO roadGroupedResponseDTO = new RoadGroupedResponseDTO();

        SearchMetaDTO meta = new SearchMetaDTO();
        List<RoadGroupedTripDTO> roadTripGroupedDTOS = new ArrayList<>();
        RoadGroupedTripDTO roadTripGroupedDTO1 = new RoadGroupedTripDTO();

        //Supplier 1
        RoadSupplierDTO roadSupplierDTO1 = new RoadSupplierDTO();
        roadSupplierDTO1.setId("9");
        //Supplier 2
        RoadSupplierDTO roadSupplierDTO2 = new RoadSupplierDTO();
        roadSupplierDTO2.setId("12");

        // Locations
        RoadGroupedLocationDTO locationDTO1 = new RoadGroupedLocationDTO();
        locationDTO1.setId("50");
        locationDTO1.setName("Location 1");
        RoadGroupedLocationDTO locationDTO2 = new RoadGroupedLocationDTO();
        locationDTO2.setId("52");
        locationDTO2.setName("Location 2");

        List<RoadTripDTO> roadTripDTOS = new ArrayList<>();
        RoadTripDTO roadTripDTO1 = new RoadTripDTO();
        roadTripDTO1.setSupplier(roadSupplierDTO1);
        roadTripDTO1.setTripId(UUID.randomUUID().toString());
        roadTripDTO1.setPromotion(null);

        RoadLocationDTO arrivalLocation = new RoadLocationDTO();
        arrivalLocation.setId("50");
        arrivalLocation.setName("Arrival Location");
        RoadLocationDTO departureLocation = new RoadLocationDTO();
        departureLocation.setId("52");
        departureLocation.setName("Departure Location");

        RoadTripStepDTO arrival1 = new RoadTripStepDTO();
        arrival1.setLocation(arrivalLocation);
        RoadTripStepDTO departure1 = new RoadTripStepDTO();
        departure1.setLocation(departureLocation);

        roadTripDTO1.setArrival(arrival1);
        roadTripDTO1.setDeparture(departure1);

        RoadTripDTO roadTripDTO2 = new RoadTripDTO();
        roadTripDTO2.setSupplier(roadSupplierDTO2);
        roadTripDTO2.setTripId(UUID.randomUUID().toString());
        roadTripDTO2.setPromotion(null);

        RoadLocationDTO arrivalLocation1 = new RoadLocationDTO();
        arrivalLocation.setId("50");
        arrivalLocation.setName("Arrival Location");
        RoadLocationDTO departureLocation2 = new RoadLocationDTO();
        departureLocation.setId("52");
        departureLocation.setName("Departure Location");

        RoadTripStepDTO arrival2 = new RoadTripStepDTO();
        arrival2.setLocation(arrivalLocation1);
        RoadTripStepDTO departure2 = new RoadTripStepDTO();
        departure2.setLocation(departureLocation2);

        roadTripDTO2.setArrival(arrival2);
        roadTripDTO2.setDeparture(departure2);

        roadTripDTOS.add(roadTripDTO1);
        roadTripDTOS.add(roadTripDTO2);

        //Supplier 3
        RoadGroupedSupplierDTO roadSupplierDTO3 = new RoadGroupedSupplierDTO();
        roadSupplierDTO1.setId("9");

        roadTripGroupedDTO1.setSupplier(roadSupplierDTO3);
        roadTripGroupedDTO1.setTimetable(roadTripDTOS);
        roadTripGroupedDTO1.setArrival(locationDTO1);
        roadTripGroupedDTO1.setDeparture(locationDTO2);

        roadTripGroupedDTOS.add(roadTripGroupedDTO1);

        roadGroupedResponseDTO.setTrips(roadTripGroupedDTOS);
        roadGroupedResponseDTO.setMeta(meta);
        return roadGroupedResponseDTO;
    }

    private RoadFilterRequestDTO getRoadFilterResponseDTO() {

        List<Integer> ages = new ArrayList<>();
        ages.add(18);
        RoadFilterRequestDTO filter = new RoadFilterRequestDTO();
        filter.setAges(ages);
        filter.setArrivalMasterCode("50");
        filter.setDepartureMasterCode("52");
        filter.setDepartureDate(LocalDate.now());

        return filter;

    }
}
