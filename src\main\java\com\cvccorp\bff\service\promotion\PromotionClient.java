package com.cvccorp.bff.service.promotion;

import com.cvccorp.bff.infra.exception.ClientExceptionMapper;
import com.cvccorp.bff.infra.filters.RequestHeadersFactory;
import com.cvccorp.bff.proxy.dto.promotion.response.OpportunitiesResponseDTO;
import org.eclipse.microprofile.rest.client.annotation.RegisterClientHeaders;
import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

@RegisterRestClient(configKey = "rest-client-api-opportunities")
@RegisterClientHeaders(RequestHeadersFactory.class)
@RegisterProvider(ClientExceptionMapper.class)
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public interface PromotionClient {

    @POST
    @Path("/opportunities")
    OpportunitiesResponseDTO opportunities(String body);

}
